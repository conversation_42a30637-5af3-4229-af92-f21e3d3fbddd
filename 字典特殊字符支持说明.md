# 字典特殊字符支持说明

## 修改概述

已修改字典数据值的验证规则，支持在特定类型的字典中使用URL路径和特殊字符，同时保持对其他字典的安全验证。

## 修改内容

### 1. 智能验证机制

系统现在会根据字典编码自动判断验证规则：

#### 特殊字典类型（宽松验证）
当字典编码包含以下关键字时，允许使用更多特殊字符：
- `navigationPath` - 快速导航路径
- `urlPath` - URL路径
- `linkPath` - 链接路径  
- `filePath` - 文件路径
- `apiPath` - API路径

**允许的字符：**
- 字母数字：`a-z A-Z 0-9`
- 路径分隔符：`/`
- 查询参数：`? & =`
- 连接符：`- _`
- 点号：`.`
- 冒号：`:`
- 括号：`( )`

**禁止的字符：**
`` ` ~ ! @ # $ ^ * | { } ' < > 《 》 ！ ￥ （ ） — 【 】 ' ； ： " " 。 ， 、 ``

#### 普通字典（严格验证）
对于其他字典，保持原有的严格验证规则，禁止大部分特殊字符以确保数据安全。

### 2. 用户友好提示

- 特殊字典：显示具体禁止的字符列表
- 普通字典：提示如需使用路径，可在字典编码中包含"Path"关键字

## 使用方法

### 创建支持URL的字典

1. **字典编码命名**：
   ```
   navigationPath    ✅ 支持路径字符
   userUrlPath      ✅ 支持路径字符
   systemFilePath   ✅ 支持路径字符
   apiEndpointPath  ✅ 支持路径字符
   ```

2. **数据值示例**：
   ```
   /scene                           ✅ 允许
   /dashboard/workbench            ✅ 允许
   https://example.com/api         ✅ 允许
   /api/user?id=123&type=admin     ✅ 允许
   C:\Program Files\App            ✅ 允许
   ```

### 普通字典使用

对于不包含"Path"关键字的字典编码，仍然使用严格验证：
```
userStatus     - 只能使用字母数字和基本字符
orderType      - 禁止特殊字符
colorCode      - 保持原有验证规则
```

## 兼容性保证

### 不受影响的功能
- 现有字典数据不受影响
- 字典翻译功能正常工作
- 字典缓存机制不变
- 其他业务逻辑保持不变

### 向后兼容
- 现有字典编码继续使用严格验证
- 只有新创建的包含"Path"关键字的字典才使用宽松验证
- 不会破坏现有业务数据

## 安全考虑

### 保留的安全措施
1. **XSS防护**：仍然禁止 `< > ' "` 等可能导致脚本注入的字符
2. **SQL注入防护**：禁止 `'` 等可能影响数据库查询的字符
3. **系统安全**：禁止 `|` 等可能影响系统命令的字符

### 允许的安全字符
只允许URL和路径中常用的安全字符，如：
- 路径分隔符：`/`
- 查询参数：`? & =`
- 端口号：`:`
- 文件扩展名：`.`

## 测试建议

### 测试用例

1. **创建navigationPath字典**：
   - 编码：`navigationPath`
   - 数据值：`/scene` ✅ 应该通过验证

2. **创建普通字典**：
   - 编码：`userType`
   - 数据值：`/scene` ❌ 应该被拒绝

3. **测试特殊字符**：
   - 路径字典中输入：`/api?id=123` ✅ 应该通过
   - 普通字典中输入：`test<script>` ❌ 应该被拒绝

### 验证步骤

1. 访问系统管理 → 数据字典
2. 创建字典编码包含"Path"的字典
3. 尝试添加包含URL路径的数据值
4. 验证是否能正常保存
5. 测试字典翻译功能是否正常

## 注意事项

1. **字典编码命名**：确保需要支持路径的字典编码包含"Path"关键字
2. **数据迁移**：现有数据不需要迁移，自动兼容
3. **权限控制**：字典管理权限要求不变
4. **缓存刷新**：修改后建议刷新字典缓存

这个修改确保了在支持URL路径的同时，不会破坏现有的业务逻辑和数据安全。
