<template>
  <div class="p-1 cursor-pointer" @click="handleNavigation">
    <a-tooltip>
      <template #title>快速导航</template>
      <Icon icon="ant-design:compass-outlined" />
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItemsByCode, initDictOptions } from '/@/utils/dict/index';
  import { Icon } from '/@/components/Icon';
  import { onMounted, onBeforeUnmount } from 'vue';
  import { useUserStore } from '/@/store/modules/user';
  import { TOKEN_KEY } from '/@/enums/cacheEnum';
  import { setAuthCache } from '/@/utils/auth';

  const router = useRouter();
  const { createMessage } = useMessage();
  const userStore = useUserStore();

  // 组件挂载时确保字典数据已加载
  onMounted(async () => {
    try {
      // 刷新用户token以确保请求能正常进行
      const currentToken = userStore.getToken;
      if (currentToken) {
        userStore.setToken(currentToken);
        setAuthCache(TOKEN_KEY, currentToken);
      }

      // 加载字典数据
      await loadDictionaryData();
    } catch (error) {
      console.error('初始化导航字典数据失败:', error);
    }
  });

  onBeforeUnmount(() => {
    // 清理工作（如果需要）
  });

  async function loadDictionaryData() {
    try {
      // 首先尝试从本地获取
      userStore.setAllDictItemsByLocal();
      const items = getDictItemsByCode('navigationPath');
      
      if (!items) {
        // 如果本地没有，则从服务器获取并等待结果
        console.log('从服务器获取字典数据');
        await initDictOptions('navigationPath');
      }
    } catch (error) {
      console.error('加载字典数据失败:', error);
      throw error;
    }
  }

  async function handleNavigation() {
    try {
      // 如果获取不到数据，先尝试重新加载
      await loadDictionaryData();
      
      // 从数据字典获取导航路径
      const navigationItems = getDictItemsByCode('navigationPath');
      
      // 打印获取到的导航项，用于调试
      console.log('获取到的导航配置项:', navigationItems);

      // 检查navigationItems是否为undefined或null
      if (!navigationItems) {
        console.warn('navigationPath返回值为空');
        createMessage.warning('未配置导航路径，请在数据字典中配置 navigationPath');
        return;
      }

      // 检查navigationItems是否为数组且有元素
      if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
        console.warn('navigationPath配置为空数组');
        createMessage.warning('未配置导航路径，请在数据字典中配置 navigationPath');
        return;
      }

      // 验证第一个导航项的值
      const firstItem = navigationItems[0];
      if (!firstItem || !firstItem.value) {
        console.error('第一个导航项无效:', firstItem);
        createMessage.error('导航路径配置错误，请检查字典项的value值');
        return;
      }

      // 执行导航
      router.push(firstItem.value);
    } catch (error) {
      console.error('导航失败，详细错误:', error);
      createMessage.error('导航失败，请刷新页面后重试');
    }
  }
</script>
