<template>
  <div class="p-1 cursor-pointer" @click="handleNavigation">
    <a-tooltip>
      <template #title>快速导航</template>
      <Icon icon="ant-design:compass-outlined" />
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItemsByCode, ajaxGetDictItems } from '/@/utils/dict/index';
  import { Icon } from '/@/components/Icon';
  import { onMounted, ref } from 'vue';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const isLoading = ref(false);

  // 组件挂载时预加载字典数据
  onMounted(async () => {
    try {
      await loadDictionaryData();
    } catch (error) {
      console.error('初始化导航字典数据失败:', error);
    }
  });

  async function loadDictionaryData() {
    try {
      // 首先尝试从缓存获取
      userStore.setAllDictItemsByLocal();
      let items = getDictItemsByCode('navigationPath');

      if (!items || !Array.isArray(items) || items.length === 0) {
        // 如果缓存中没有，则从服务器获取
        console.log('从服务器获取navigationPath字典数据');
        const response = await ajaxGetDictItems('navigationPath', {});

        if (response && Array.isArray(response) && response.length > 0) {
          // 更新用户store中的字典数据
          const currentDictItems = userStore.getAllDictItems || {};
          (currentDictItems as any)['navigationPath'] = response;
          userStore.setAllDictItems(currentDictItems);
          items = response;
        }
      }

      console.log('加载的navigationPath数据:', items);
      return items;
    } catch (error) {
      console.error('加载字典数据失败:', error);
      throw error;
    }
  }

  async function handleNavigation() {
    if (isLoading.value) {
      return;
    }

    try {
      isLoading.value = true;

      // 获取导航路径配置
      let navigationItems = getDictItemsByCode('navigationPath');

      // 如果缓存中没有，尝试重新加载
      if (!navigationItems || !Array.isArray(navigationItems) || navigationItems.length === 0) {
        navigationItems = await loadDictionaryData();
      }

      console.log('获取到的导航配置项:', navigationItems);

      // 检查数据有效性
      if (!navigationItems || !Array.isArray(navigationItems) || navigationItems.length === 0) {
        createMessage.warning('未配置导航路径，请在数据字典中配置 navigationPath 字典项');
        return;
      }

      // 验证第一个导航项
      const firstItem = navigationItems[0];
      if (!firstItem || !firstItem.value) {
        console.error('第一个导航项无效:', firstItem);
        createMessage.error('导航路径配置错误，请检查字典项的value值');
        return;
      }

      console.log('准备导航到:', firstItem.value);

      // 执行导航
      await router.push(firstItem.value);
      createMessage.success('导航成功');
    } catch (error) {
      console.error('导航失败，详细错误:', error);
      const errorMessage = error instanceof Error ? error.message : '请检查路径配置或刷新页面后重试';
      createMessage.error(`导航失败: ${errorMessage}`);
    } finally {
      isLoading.value = false;
    }
  }
</script>
