<template>
  <div class="p-1 cursor-pointer" @click="handleNavigation">
    <a-tooltip>
      <template #title>快速导航</template>
      <Icon icon="ant-design:compass-outlined" />
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItemsByCode, ajaxGetDictItems } from '/@/utils/dict/index';
  import { Icon } from '/@/components/Icon';
  import { onMounted, ref } from 'vue';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const isLoading = ref(false);

  // 组件挂载时预加载字典数据
  onMounted(async () => {
    try {
      await loadDictionaryData();
    } catch (error) {
      console.error('初始化导航字典数据失败:', error);
    }
  });

  async function loadDictionaryData() {
    try {
      // 首先尝试从缓存获取
      userStore.setAllDictItemsByLocal();
      let items = getDictItemsByCode('navigationPath');

      if (!items || !Array.isArray(items) || items.length === 0) {
        // 如果缓存中没有，则从服务器获取
        console.log('从服务器获取navigationPath字典数据');
        console.log('当前时间戳:', new Date().getTime());
        console.log('当前用户Token:', userStore.getToken ? '已存在' : '不存在');

        const response = await ajaxGetDictItems('navigationPath', {});

        if (response && Array.isArray(response) && response.length > 0) {
          // 更新用户store中的字典数据
          const currentDictItems = userStore.getAllDictItems || {};
          (currentDictItems as any)['navigationPath'] = response;
          userStore.setAllDictItems(currentDictItems);
          items = response;
          console.log('成功从服务器获取字典数据:', items);
        } else {
          console.warn('服务器返回的字典数据为空或格式不正确:', response);
        }
      } else {
        console.log('从缓存获取到字典数据:', items);
      }

      return items;
    } catch (error) {
      console.error('加载字典数据失败:', error);
      // 检查是否是时间戳相关错误
      if (error && typeof error === 'object' && 'message' in error) {
        const errorMessage = (error as any).message || '';
        if (errorMessage.includes('X-TIMESTAMP') || errorMessage.includes('签名验证失败')) {
          console.error('时间戳验证失败，可能的原因：');
          console.error('1. 客户端和服务器时间不同步');
          console.error('2. Token已过期');
          console.error('3. 网络延迟导致请求超时');
          console.error('当前客户端时间:', new Date().toISOString());
        }
      }
      throw error;
    }
  }

  async function handleNavigation() {
    if (isLoading.value) {
      return;
    }

    try {
      isLoading.value = true;

      // 首先尝试从缓存获取导航路径配置
      userStore.setAllDictItemsByLocal();
      let navigationItems = getDictItemsByCode('navigationPath');

      console.log('获取到的导航配置项:', navigationItems);

      // 如果缓存中没有数据，提供默认路径或提示用户配置
      if (!navigationItems || !Array.isArray(navigationItems) || navigationItems.length === 0) {
        // 尝试从服务器获取（可能会遇到时间戳问题）
        try {
          console.log('缓存中无数据，尝试从服务器获取...');
          navigationItems = await loadDictionaryData();
        } catch (apiError) {
          console.error('API调用失败:', apiError);

          // 如果是时间戳错误，提供临时解决方案
          const errorMsg = apiError instanceof Error ? apiError.message : '';
          if (errorMsg.includes('X-TIMESTAMP') || errorMsg.includes('签名验证失败')) {
            createMessage.warning(
              '检测到时间戳验证问题，请尝试以下解决方案：\n1. 刷新页面重新登录\n2. 检查系统时间是否正确\n3. 联系管理员配置字典数据'
            );

            // 提供默认的导航路径作为临时解决方案
            const defaultPath = '/scene';
            console.log('使用默认路径:', defaultPath);
            await router.push(defaultPath);
            createMessage.info(`已导航到默认页面: ${defaultPath}`);
            return;
          } else {
            throw apiError; // 重新抛出非时间戳相关的错误
          }
        }
      }

      // 检查数据有效性
      if (!navigationItems || !Array.isArray(navigationItems) || navigationItems.length === 0) {
        createMessage.warning('未配置导航路径，请在系统管理→数据字典中配置 navigationPath 字典项');
        return;
      }

      // 验证第一个导航项
      const firstItem = navigationItems[0];
      if (!firstItem || !firstItem.value) {
        console.error('第一个导航项无效:', firstItem);
        createMessage.error('导航路径配置错误，请检查字典项的value值');
        return;
      }

      console.log('准备导航到:', firstItem.value);

      // 执行导航
      await router.push(firstItem.value);
      createMessage.success(`导航成功: ${firstItem.text || firstItem.value}`);
    } catch (error) {
      console.error('导航失败，详细错误:', error);
      const errorMessage = error instanceof Error ? error.message : '请检查路径配置或刷新页面后重试';
      createMessage.error(`导航失败: ${errorMessage}`);
    } finally {
      isLoading.value = false;
    }
  }
</script>
