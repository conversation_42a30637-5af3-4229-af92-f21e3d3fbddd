# 快速导航功能配置说明

## 问题分析

### 主要问题：时间戳验证失败
错误信息："操作失败，签名验证失败:X-TIMESTAMP已过期"

**原因分析：**
1. **系统时间不同步** - 客户端和服务器时间差异过大
2. **Token过期** - 用户登录状态已失效
3. **网络延迟** - 请求发送时间过长导致时间戳过期
4. **签名计算错误** - 请求参数或签名算法问题

### 已修复的问题：

1. **Icon组件导入错误** - 修正了导入路径
2. **字典数据获取逻辑问题** - 优化了数据获取和缓存机制
3. **错误处理优化** - 增加了时间戳错误的特殊处理
4. **默认路径支持** - 当API调用失败时提供默认导航路径

## 配置步骤

### 1. 访问数据字典管理页面
- 路径：`/system/dict`
- 菜单：系统管理 → 数据字典

### 2. 创建字典
点击"新增"按钮，填写以下信息：
- **字典名称**：快速导航路径
- **字典编码**：`navigationPath` （重要：必须包含"Path"关键字以支持路径字符）
- **描述**：快速导航功能使用的路径配置

> **注意**：字典编码必须包含"Path"关键字（如navigationPath、urlPath等），这样系统会自动允许输入URL路径和特殊字符。

### 3. 配置字典项
在字典列表中找到刚创建的字典，点击"字典配置"按钮，然后添加字典项：

#### 示例配置：
- **名称**：3D场景
- **数据值**：`/scene`
- **描述**：跳转到3D场景页面
- **排序**：1
- **是否启用**：是

#### 其他可选路径：
- **名称**：工作台，**数据值**：`/dashboard/workbench`
- **名称**：资产管理，**数据值**：`/asset/management`
- **名称**：BA系统，**数据值**：`/ba/system`

### 4. 刷新字典缓存
配置完成后，在数据字典页面点击"刷新缓存"按钮，确保新配置生效。

## 功能说明

- 快速导航按钮位于页面头部右侧（指南针图标）
- 点击后会自动跳转到字典中配置的第一个路径
- 如果未配置字典或配置错误，会显示相应的提示信息
- 支持任何有效的Vue Router路径

## 注意事项

1. 字典编码必须是 `navigationPath`（区分大小写）
2. 数据值必须是有效的路由路径
3. 系统会使用第一个启用的字典项作为导航目标
4. 配置后需要刷新字典缓存才能生效

## 测试方法

1. 按照上述步骤配置字典
2. 刷新页面
3. 点击页面头部的快速导航按钮（指南针图标）
4. 检查是否正确跳转到配置的页面

如果仍有问题，请检查浏览器控制台的错误信息。

## 时间戳验证失败解决方案

### 临时解决方案
如果遇到"X-TIMESTAMP已过期"错误，快速导航功能现在会：
1. 自动使用默认路径 `/scene` 进行导航
2. 显示友好的错误提示和解决建议

### 根本解决方案

#### 1. 检查系统时间
确保客户端和服务器时间同步：
- Windows: 右键点击时间 → 调整日期/时间 → 立即同步
- Mac: 系统偏好设置 → 日期与时间 → 自动设置日期和时间
- Linux: `sudo ntpdate -s time.nist.gov`

#### 2. 刷新登录状态
- 完全刷新浏览器页面（Ctrl+F5 或 Cmd+Shift+R）
- 重新登录系统
- 清除浏览器缓存和Cookie

#### 3. 检查网络连接
- 确保网络连接稳定
- 检查是否有代理或防火墙影响
- 尝试切换网络环境

#### 4. 联系系统管理员
如果以上方法都无效，请联系系统管理员：
- 检查服务器时间设置
- 调整时间戳验证窗口
- 检查签名验证配置

### 开发者调试信息
在浏览器控制台中可以看到详细的调试信息：
- 当前时间戳
- Token状态
- 字典数据获取过程
- 错误详细信息

这些信息有助于定位具体问题原因。
